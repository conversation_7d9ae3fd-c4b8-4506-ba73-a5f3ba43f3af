#!/usr/bin/env python3
"""
Check what content was filtered out during processing
"""

import os
import sys
import re
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the class directly
import importlib.util
spec = importlib.util.spec_from_file_location("generate_notes_anchor", "generate-notes-anchor.py")
generate_notes_anchor = importlib.util.module_from_spec(spec)
spec.loader.exec_module(generate_notes_anchor)
NotesAnchorGenerator = generate_notes_anchor.NotesAnchorGenerator


def check_filtering():
    """Check what content gets filtered"""
    print("🔍 Checking filtering functionality...")
    
    file_path = "chemistry_notes_markdown/1b Redox Reactions.md"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # Patterns to filter out
    filter_patterns = [
        re.compile(r'raffles\s+institution', re.IGNORECASE),
        re.compile(r'page[-\s]*\d+', re.IGNORECASE),
    ]
    
    filtered_lines = []
    total_lines = 0
    
    for line_num, line in enumerate(lines, 1):
        stripped_line = line.strip()
        if not stripped_line:
            continue
            
        total_lines += 1
        
        for pattern in filter_patterns:
            if pattern.search(stripped_line):
                filtered_lines.append((line_num, stripped_line, pattern.pattern))
                break
    
    print(f"📊 Filtering Results:")
    print(f"   Total non-empty lines: {total_lines}")
    print(f"   Filtered lines: {len(filtered_lines)}")
    
    if filtered_lines:
        print(f"\n🚫 Filtered content:")
        for line_num, content, pattern in filtered_lines[:10]:  # Show first 10
            print(f"   Line {line_num}: {content[:80]}...")
            print(f"   Matched pattern: {pattern}")
            print()
        
        if len(filtered_lines) > 10:
            print(f"   ... and {len(filtered_lines) - 10} more filtered lines")
    else:
        print(f"   ✅ No content was filtered")
    
    # Test the generator's filtering
    print(f"\n🧪 Testing NotesAnchorGenerator filtering:")
    generator = NotesAnchorGenerator()
    
    test_texts = [
        "Raffles Institution",
        "Year 5 H2 Chemistry 2023",
        "Page 1",
        "Page-2",
        "## Page 3",
        "Normal content about redox reactions",
        "The Raffles Institution logo",
        "This is page 5 content"
    ]
    
    for text in test_texts:
        should_filter = generator.should_filter_chunk(text)
        status = "🚫 FILTERED" if should_filter else "✅ KEPT"
        print(f"   {status}: {text}")


if __name__ == "__main__":
    check_filtering()
