#!/usr/bin/env python3
"""
Verify that chunks were stored correctly in the database
and check filtering functionality
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import ChemistryChapter, ChapterSection, SectionContent
from config import Config


def verify_chunks():
    """Verify chunks in database and check filtering"""
    print("🔍 Verifying chunks in database...")
    
    # Setup database
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Get the redox reactions chapter
        chapter = session.query(ChemistryChapter).filter_by(slug='redox-reactions').first()
        if not chapter:
            print("❌ Redox Reactions chapter not found!")
            return
        
        print(f"📖 Found chapter: {chapter.title}")
        print(f"   Slug: {chapter.slug}")
        print(f"   File path: {chapter.file_path}")
        
        # Get all sections for this chapter
        sections = session.query(ChapterSection).filter_by(chapter_id=chapter.id).all()
        print(f"📄 Found {len(sections)} sections")
        
        # Get all content blocks
        total_content = 0
        content_types = {}
        filtered_content = []
        
        for section in sections:
            content_blocks = session.query(SectionContent).filter_by(section_id=section.id).all()
            total_content += len(content_blocks)
            
            for content in content_blocks:
                # Count content types
                if content.content_type not in content_types:
                    content_types[content.content_type] = 0
                content_types[content.content_type] += 1
                
                # Check for filtered content (should not exist)
                if 'raffles institution' in content.content_data.lower():
                    filtered_content.append(('Raffles Institution', content.content_data[:100]))
                elif 'page-' in content.content_data.lower() or 'page ' in content.content_data.lower():
                    if content.content_data.lower().strip().startswith('page '):
                        filtered_content.append(('Page marker', content.content_data[:100]))
        
        print(f"📊 Content Statistics:")
        print(f"   Total content blocks: {total_content}")
        for content_type, count in content_types.items():
            print(f"   {content_type}: {count}")
        
        # Check filtering effectiveness
        print(f"\n🔍 Filtering Check:")
        if filtered_content:
            print(f"   ⚠️  Found {len(filtered_content)} items that should have been filtered:")
            for filter_type, content_preview in filtered_content[:5]:
                print(f"     - {filter_type}: {content_preview}...")
        else:
            print(f"   ✅ No filtered content found - filtering worked correctly!")
        
        # Show sample chunks
        print(f"\n📝 Sample chunks:")
        sample_content = session.query(SectionContent).filter_by(section_id=sections[0].id).limit(3).all()
        for i, content in enumerate(sample_content, 1):
            print(f"   {i}. Type: {content.content_type}")
            print(f"      Anchor: {content.meta_data.get('anchor_id', 'N/A') if content.meta_data else 'N/A'}")
            print(f"      Content: {content.content_data[:100]}...")
            print()
        
        # Check for specific anchor IDs
        print(f"🔗 Anchor ID Examples:")
        anchor_examples = []
        for section in sections[:5]:
            content_blocks = session.query(SectionContent).filter_by(section_id=section.id).limit(1).all()
            for content in content_blocks:
                if content.meta_data and 'anchor_id' in content.meta_data:
                    anchor_examples.append(content.meta_data['anchor_id'])
        
        for anchor in anchor_examples:
            print(f"   - {anchor}")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        session.close()


if __name__ == "__main__":
    verify_chunks()
