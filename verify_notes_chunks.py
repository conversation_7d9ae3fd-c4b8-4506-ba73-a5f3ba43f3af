#!/usr/bin/env python3
"""
Verify that chunks were stored correctly in the NotesChunk table
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import NotesChunk
from config import Config


def verify_notes_chunks():
    """Verify chunks in NotesChunk table"""
    print("🔍 Verifying chunks in NotesChunk table...")
    
    # Setup database
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Get total count
        total_chunks = session.query(NotesChunk).count()
        print(f"📊 Total chunks in database: {total_chunks}")
        
        if total_chunks == 0:
            print("❌ No chunks found in database!")
            return
        
        # Get chunks by type
        from sqlalchemy import func
        chunk_types = session.query(NotesChunk.chunk_type, func.count(NotesChunk.id)).group_by(NotesChunk.chunk_type).all()

        print(f"\n📈 Chunks by type:")
        for chunk_type, count in chunk_types:
            print(f"   {chunk_type}: {count}")

        # Get chunks by chapter
        chapters = session.query(NotesChunk.chapter_slug, func.count(NotesChunk.id)).group_by(NotesChunk.chapter_slug).all()
        
        print(f"\n📚 Chunks by chapter:")
        for chapter_slug, count in chapters:
            print(f"   {chapter_slug}: {count}")
        
        # Show sample chunks
        print(f"\n📝 Sample chunks:")
        sample_chunks = session.query(NotesChunk).limit(5).all()
        for i, chunk in enumerate(sample_chunks, 1):
            print(f"   {i}. Type: {chunk.chunk_type}")
            print(f"      Anchor: {chunk.anchor_id}")
            print(f"      Chapter: {chunk.chapter_slug}")
            print(f"      Section: {chunk.section_title}")
            print(f"      Page: {chunk.page_number}")
            print(f"      Text: {chunk.text[:100]}...")
            print()
        
        # Check for filtered content (should not exist)
        print(f"🔍 Checking for filtered content:")
        raffles_chunks = session.query(NotesChunk).filter(
            NotesChunk.text.like('%Raffles Institution%')
        ).count()
        
        page_chunks = session.query(NotesChunk).filter(
            NotesChunk.text.like('%Page %')
        ).count()
        
        print(f"   Chunks containing 'Raffles Institution': {raffles_chunks}")
        print(f"   Chunks containing 'Page ': {page_chunks}")
        
        if raffles_chunks == 0 and page_chunks == 0:
            print(f"   ✅ Filtering worked correctly - no unwanted content found!")
        else:
            print(f"   ⚠️  Some filtered content may have slipped through")
        
        # Check anchor ID uniqueness
        print(f"\n🔗 Checking anchor ID uniqueness:")
        unique_anchors = session.query(NotesChunk.anchor_id).distinct().count()
        total_anchors = session.query(NotesChunk.anchor_id).count()
        
        print(f"   Total anchor IDs: {total_anchors}")
        print(f"   Unique anchor IDs: {unique_anchors}")
        
        if unique_anchors == total_anchors:
            print(f"   ✅ All anchor IDs are unique!")
        else:
            print(f"   ❌ Found {total_anchors - unique_anchors} duplicate anchor IDs")
        
        # Show some anchor ID examples
        print(f"\n🔗 Sample anchor IDs:")
        anchor_samples = session.query(NotesChunk.anchor_id).limit(10).all()
        for anchor in anchor_samples:
            print(f"   - {anchor[0]}")
        
        # Check file path
        filepaths = session.query(NotesChunk.filepath).distinct().all()
        print(f"\n📁 Files processed:")
        for filepath in filepaths:
            count = session.query(NotesChunk).filter_by(filepath=filepath[0]).count()
            print(f"   {filepath[0]}: {count} chunks")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        session.close()


if __name__ == "__main__":
    verify_notes_chunks()
