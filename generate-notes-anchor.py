#!/usr/bin/env python3
"""
Generate Notes Anchor Script
Chunks markdown files in chemistry_notes_markdown and stores each chunk into database
with chunk type, filepath, anchor id, and text.

Filters out chunks containing "Raffles Institution" or "Page-X" patterns.
"""

import os
import sys
import re
from pathlib import Path
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import db, NotesChunk
from config import Config


@dataclass
class ChunkData:
    """Represents a chunk of content from markdown files"""
    chunk_type: str  # 'heading', 'text', 'equation', 'image', 'table'
    filepath: str
    anchor_id: str
    text: str
    chapter_slug: str
    section_title: str
    heading_level: Optional[int] = None
    page_number: Optional[int] = None


class NotesAnchorGenerator:
    """Generates anchor IDs and chunks markdown content"""
    
    def __init__(self, notes_directory: str = "chemistry_notes_markdown"):
        self.notes_directory = Path(notes_directory)
        self.heading_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        self.page_pattern = re.compile(r'^##\s+Page\s+(\d+)$', re.MULTILINE)
        self.image_pattern = re.compile(r'!\[([^\]]*)\]\(([^)]+)\)')
        self.equation_pattern = re.compile(r'\$[^$]+\$|\$\$[^$]+\$\$')
        
        # Patterns to filter out
        self.filter_patterns = [
            re.compile(r'raffles\s+institution', re.IGNORECASE),
            re.compile(r'page[-\s]*\d+', re.IGNORECASE),
        ]
    
    def should_filter_chunk(self, text: str) -> bool:
        """Check if chunk should be filtered out"""
        for pattern in self.filter_patterns:
            if pattern.search(text):
                return True
        return False
    
    def generate_chapter_slug(self, title: str) -> str:
        """Generate URL-friendly slug from chapter title"""
        # Remove chapter numbers and clean up
        clean_title = re.sub(r'^\d+[a-z]?\s+', '', title)
        # Convert to lowercase and replace spaces/special chars with hyphens
        slug = re.sub(r'[^\w\s-]', '', clean_title.lower())
        slug = re.sub(r'\s+', '-', slug.strip())
        slug = re.sub(r'-+', '-', slug)
        return slug.strip('-')[:50]
    
    def generate_anchor_id(self, text: str, chapter_slug: str, chunk_type: str, counter: int) -> str:
        """Generate a clean anchor ID for the chunk"""
        # Clean the text
        clean_text = re.sub(r'[^\w\s-]', '', text.lower())
        clean_text = re.sub(r'\s+', '-', clean_text.strip())
        clean_text = re.sub(r'-+', '-', clean_text)
        clean_text = clean_text.strip('-')
        
        # Limit length
        if len(clean_text) > 30:
            clean_text = clean_text[:30].rstrip('-')
        
        return f"{chapter_slug}-{chunk_type}-{clean_text}-{counter}"
    
    def extract_chapter_info(self, file_path: str) -> Dict[str, str]:
        """Extract chapter metadata from file path"""
        filename = os.path.basename(file_path)
        
        # Extract chapter number and title from filename
        match = re.match(r'^(\d+[a-z]?)\s+(.+)\.md$', filename)
        if match:
            chapter_number = match.group(1)
            title = match.group(2)
        else:
            chapter_number = None
            title = filename.replace('.md', '')
        
        slug = self.generate_chapter_slug(title)
        
        return {
            'title': title,
            'chapter_number': chapter_number,
            'slug': slug,
            'file_path': file_path
        }

    def chunk_markdown_file(self, file_path: str) -> List[ChunkData]:
        """Chunk a markdown file into content pieces with anchor IDs"""
        print(f"📄 Processing file: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        chapter_info = self.extract_chapter_info(file_path)
        chunks = []

        lines = content.split('\n')
        current_chunk_content = []
        current_page = None
        current_section_title = "Introduction"

        # Counters for unique anchor IDs
        counters = {'heading': 0, 'text': 0, 'equation': 0, 'image': 0, 'table': 0}

        for line_num, line in enumerate(lines):
            stripped_line = line.strip()

            # Skip empty lines
            if not stripped_line:
                if current_chunk_content:
                    current_chunk_content.append('')
                continue

            # Check for page markers
            page_match = self.page_pattern.match(line)
            if page_match:
                current_page = int(page_match.group(1))
                # Save previous text chunk if exists
                if current_chunk_content:
                    text_content = '\n'.join(current_chunk_content).strip()
                    if text_content and not self.should_filter_chunk(text_content):
                        counters['text'] += 1
                        anchor_id = self.generate_anchor_id(
                            text_content[:50], chapter_info['slug'], 'text', counters['text']
                        )
                        chunks.append(ChunkData(
                            chunk_type='text',
                            filepath=file_path,
                            anchor_id=anchor_id,
                            text=text_content,
                            chapter_slug=chapter_info['slug'],
                            section_title=current_section_title,
                            page_number=current_page
                        ))
                    current_chunk_content = []
                continue

            # Check for headings
            heading_match = self.heading_pattern.match(line)
            if heading_match:
                # Save previous text chunk if exists
                if current_chunk_content:
                    text_content = '\n'.join(current_chunk_content).strip()
                    if text_content and not self.should_filter_chunk(text_content):
                        counters['text'] += 1
                        anchor_id = self.generate_anchor_id(
                            text_content[:50], chapter_info['slug'], 'text', counters['text']
                        )
                        chunks.append(ChunkData(
                            chunk_type='text',
                            filepath=file_path,
                            anchor_id=anchor_id,
                            text=text_content,
                            chapter_slug=chapter_info['slug'],
                            section_title=current_section_title,
                            page_number=current_page
                        ))
                    current_chunk_content = []

                # Process heading
                heading_level = len(heading_match.group(1))
                heading_text = heading_match.group(2).strip()
                current_section_title = heading_text

                if not self.should_filter_chunk(heading_text):
                    counters['heading'] += 1
                    anchor_id = self.generate_anchor_id(
                        heading_text, chapter_info['slug'], 'heading', counters['heading']
                    )
                    chunks.append(ChunkData(
                        chunk_type='heading',
                        filepath=file_path,
                        anchor_id=anchor_id,
                        text=heading_text,
                        chapter_slug=chapter_info['slug'],
                        section_title=heading_text,
                        heading_level=heading_level,
                        page_number=current_page
                    ))
                continue

            # Check for images
            if self.image_pattern.search(line):
                # Save previous text chunk if exists
                if current_chunk_content:
                    text_content = '\n'.join(current_chunk_content).strip()
                    if text_content and not self.should_filter_chunk(text_content):
                        counters['text'] += 1
                        anchor_id = self.generate_anchor_id(
                            text_content[:50], chapter_info['slug'], 'text', counters['text']
                        )
                        chunks.append(ChunkData(
                            chunk_type='text',
                            filepath=file_path,
                            anchor_id=anchor_id,
                            text=text_content,
                            chapter_slug=chapter_info['slug'],
                            section_title=current_section_title,
                            page_number=current_page
                        ))
                    current_chunk_content = []

                # Process image
                if not self.should_filter_chunk(line):
                    counters['image'] += 1
                    anchor_id = self.generate_anchor_id(
                        'image', chapter_info['slug'], 'image', counters['image']
                    )
                    chunks.append(ChunkData(
                        chunk_type='image',
                        filepath=file_path,
                        anchor_id=anchor_id,
                        text=line,
                        chapter_slug=chapter_info['slug'],
                        section_title=current_section_title,
                        page_number=current_page
                    ))
                continue

            # Check for equations (LaTeX)
            if self.equation_pattern.search(line):
                # Save previous text chunk if exists
                if current_chunk_content:
                    text_content = '\n'.join(current_chunk_content).strip()
                    if text_content and not self.should_filter_chunk(text_content):
                        counters['text'] += 1
                        anchor_id = self.generate_anchor_id(
                            text_content[:50], chapter_info['slug'], 'text', counters['text']
                        )
                        chunks.append(ChunkData(
                            chunk_type='text',
                            filepath=file_path,
                            anchor_id=anchor_id,
                            text=text_content,
                            chapter_slug=chapter_info['slug'],
                            section_title=current_section_title,
                            page_number=current_page
                        ))
                    current_chunk_content = []

                # Process equation
                if not self.should_filter_chunk(line):
                    counters['equation'] += 1
                    anchor_id = self.generate_anchor_id(
                        'equation', chapter_info['slug'], 'equation', counters['equation']
                    )
                    chunks.append(ChunkData(
                        chunk_type='equation',
                        filepath=file_path,
                        anchor_id=anchor_id,
                        text=line,
                        chapter_slug=chapter_info['slug'],
                        section_title=current_section_title,
                        page_number=current_page
                    ))
                continue

            # Check for tables (markdown table format)
            if '|' in line and line.count('|') >= 2:
                # This might be part of a table
                # For now, treat as regular text but could be enhanced
                pass

            # Regular text line
            current_chunk_content.append(line)

        # Save final text chunk if exists
        if current_chunk_content:
            text_content = '\n'.join(current_chunk_content).strip()
            if text_content and not self.should_filter_chunk(text_content):
                counters['text'] += 1
                anchor_id = self.generate_anchor_id(
                    text_content[:50], chapter_info['slug'], 'text', counters['text']
                )
                chunks.append(ChunkData(
                    chunk_type='text',
                    filepath=file_path,
                    anchor_id=anchor_id,
                    text=text_content,
                    chapter_slug=chapter_info['slug'],
                    section_title=current_section_title,
                    page_number=current_page
                ))

        print(f"   ✅ Generated {len(chunks)} chunks")
        return chunks

    def store_chunks_to_database(self, chunks: List[ChunkData], session) -> None:
        """Store chunks to database using NotesChunk model"""
        print(f"💾 Storing {len(chunks)} chunks to database...")

        stored_count = 0
        skipped_count = 0

        for chunk in chunks:
            # Check if chunk already exists (by anchor_id)
            existing_chunk = session.query(NotesChunk).filter_by(anchor_id=chunk.anchor_id).first()

            if existing_chunk:
                skipped_count += 1
                continue

            # Create new NotesChunk record
            notes_chunk = NotesChunk(
                chunk_type=chunk.chunk_type,
                filepath=chunk.filepath,
                anchor_id=chunk.anchor_id,
                text=chunk.text,
                chapter_slug=chunk.chapter_slug,
                section_title=chunk.section_title,
                heading_level=chunk.heading_level,
                page_number=chunk.page_number
            )

            session.add(notes_chunk)
            stored_count += 1

        # Commit all changes
        session.commit()

        print(f"   ✅ Successfully stored {stored_count} new chunks to database")
        if skipped_count > 0:
            print(f"   ℹ️  Skipped {skipped_count} existing chunks")


def main():
    """Main function to process a single file and store chunks"""
    import argparse

    parser = argparse.ArgumentParser(description='Generate anchor IDs and chunk markdown files')
    parser.add_argument('--file', '-f', default='chemistry_notes_markdown/1b Redox Reactions.md',
                        help='Markdown file to process (default: 1b Redox Reactions.md)')
    parser.add_argument('--all', '-a', action='store_true',
                        help='Process all markdown files in chemistry_notes_markdown directory')

    args = parser.parse_args()

    print("🧪 Starting Notes Anchor Generation...")

    # Setup database
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
    Session = sessionmaker(bind=engine)
    session = Session()

    generator = NotesAnchorGenerator()

    try:
        if args.all:
            # Process all markdown files
            markdown_files = list(generator.notes_directory.glob("*.md"))
            print(f"📚 Found {len(markdown_files)} markdown files to process")

            all_chunks = []
            for file_path in markdown_files:
                chunks = generator.chunk_markdown_file(str(file_path))
                all_chunks.extend(chunks)

            print(f"\n📊 Total chunks generated: {len(all_chunks)}")
            generator.store_chunks_to_database(all_chunks, session)

        else:
            # Process single file
            file_path = args.file
            if not os.path.exists(file_path):
                print(f"❌ File not found: {file_path}")
                sys.exit(1)

            chunks = generator.chunk_markdown_file(file_path)

            # Display sample chunks
            print(f"\n📊 Generated {len(chunks)} chunks:")
            for i, chunk in enumerate(chunks[:5]):  # Show first 5 chunks
                print(f"   {i+1}. Type: {chunk.chunk_type}")
                print(f"      Anchor: {chunk.anchor_id}")
                print(f"      Text: {chunk.text[:100]}...")
                print(f"      Section: {chunk.section_title}")
                print()

            if len(chunks) > 5:
                print(f"   ... and {len(chunks) - 5} more chunks")

            # Store to database
            generator.store_chunks_to_database(chunks, session)

        print(f"\n🎉 Processing completed successfully!")

    except Exception as e:
        session.rollback()
        print(f"\n❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

    finally:
        session.close()


if __name__ == "__main__":
    main()
