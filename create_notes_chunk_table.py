#!/usr/bin/env python3
"""
Migration script to create the notes_chunk table
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import db, NotesChunk
from config import Config


def create_notes_chunk_table():
    """Create the notes_chunk table if it doesn't exist"""
    print("🔄 Creating notes_chunk table...")
    
    # Setup database
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Check if table already exists
        result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='notes_chunk'"))
        table_exists = result.fetchone() is not None
        
        if table_exists:
            print("ℹ️  Table 'notes_chunk' already exists")
            
            # Check if we need to add any missing columns
            result = session.execute(text("PRAGMA table_info(notes_chunk)"))
            existing_columns = {row[1] for row in result.fetchall()}
            
            required_columns = {
                'id', 'chunk_type', 'filepath', 'anchor_id', 'text', 
                'chapter_slug', 'section_title', 'heading_level', 
                'page_number', 'created_at', 'updated_at'
            }
            
            missing_columns = required_columns - existing_columns
            if missing_columns:
                print(f"⚠️  Missing columns: {missing_columns}")
                print("🔄 Adding missing columns...")
                
                # Add missing columns one by one
                column_definitions = {
                    'chunk_type': 'VARCHAR(50) NOT NULL DEFAULT "text"',
                    'filepath': 'VARCHAR(500) NOT NULL DEFAULT ""',
                    'anchor_id': 'VARCHAR(200) NOT NULL DEFAULT ""',
                    'text': 'TEXT NOT NULL DEFAULT ""',
                    'chapter_slug': 'VARCHAR(100) NOT NULL DEFAULT ""',
                    'section_title': 'VARCHAR(500) NOT NULL DEFAULT ""',
                    'heading_level': 'INTEGER',
                    'page_number': 'INTEGER',
                    'created_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                    'updated_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP'
                }
                
                for column in missing_columns:
                    if column in column_definitions:
                        try:
                            session.execute(text(f"ALTER TABLE notes_chunk ADD COLUMN {column} {column_definitions[column]}"))
                            print(f"   ✅ Added column: {column}")
                        except Exception as e:
                            print(f"   ❌ Failed to add column {column}: {e}")
                
                session.commit()
            else:
                print("✅ All required columns exist")
        else:
            print("🔄 Creating new notes_chunk table...")
            
            # Create the table using SQLAlchemy
            NotesChunk.__table__.create(engine, checkfirst=True)
            
            print("✅ Successfully created notes_chunk table")
        
        # Create indexes if they don't exist
        print("🔄 Creating indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_notes_chunk_chapter_slug ON notes_chunk(chapter_slug)",
            "CREATE INDEX IF NOT EXISTS idx_notes_chunk_chunk_type ON notes_chunk(chunk_type)",
            "CREATE INDEX IF NOT EXISTS idx_notes_chunk_filepath ON notes_chunk(filepath)",
            "CREATE UNIQUE INDEX IF NOT EXISTS idx_notes_chunk_anchor_id ON notes_chunk(anchor_id)"
        ]
        
        for index_sql in indexes:
            try:
                session.execute(text(index_sql))
                print(f"   ✅ Created index")
            except Exception as e:
                print(f"   ⚠️  Index creation warning: {e}")
        
        session.commit()
        
        # Verify table structure
        print("\n🔍 Verifying table structure...")
        result = session.execute(text("PRAGMA table_info(notes_chunk)"))
        columns = result.fetchall()
        
        print("📊 Table columns:")
        for col in columns:
            print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        print(f"\n🎉 Migration completed successfully!")
        
    except Exception as e:
        session.rollback()
        print(f"❌ Error during migration: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        session.close()


if __name__ == "__main__":
    create_notes_chunk_table()
